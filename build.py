#!/usr/bin/env python3
"""
Master build script for Army Equipment Inventory System
Automatically detects platform and builds appropriate packages
"""

import sys
import platform
import subprocess
import argparse
from pathlib import Path

def main():
    """Main entry point for build script"""
    parser = argparse.ArgumentParser(
        description="Build Army Equipment Inventory System for current platform"
    )
    parser.add_argument(
        "--clean", 
        action="store_true", 
        help="Clean build directories before building"
    )
    parser.add_argument(
        "--no-installer", 
        action="store_true", 
        help="Skip installer creation"
    )
    parser.add_argument(
        "--test", 
        action="store_true", 
        help="Run tests after building"
    )
    parser.add_argument(
        "--verbose", 
        action="store_true", 
        help="Enable verbose output"
    )
    
    args = parser.parse_args()
    
    # Detect platform
    current_platform = platform.system().lower()
    
    print("=" * 60)
    print("Army Equipment Inventory System - Build Script")
    print("=" * 60)
    print(f"Platform: {current_platform}")
    print(f"Python: {sys.version}")
    print(f"Architecture: {platform.machine()}")
    print()
    
    # Check if we're in the right directory
    if not Path("main.py").exists():
        print("Error: main.py not found. Please run this script from the project root directory.")
        sys.exit(1)
    
    # Run the appropriate build script
    try:
        if current_platform == "windows":
            print("Running Windows build...")
            if Path("build_windows.bat").exists():
                subprocess.run(["build_windows.bat"], check=True)
            else:
                print("build_windows.bat not found, using Python build script...")
                subprocess.run([sys.executable, "build_cross_platform.py"], check=True)
                
        elif current_platform == "darwin":
            print("Running macOS build...")
            if Path("build_macos.sh").exists():
                subprocess.run(["bash", "build_macos.sh"], check=True)
            else:
                print("build_macos.sh not found, using Python build script...")
                subprocess.run([sys.executable, "build_cross_platform.py"], check=True)
                
        elif current_platform == "linux":
            print("Running Linux build...")
            print("Note: Linux builds are experimental")
            subprocess.run([sys.executable, "build_cross_platform.py"], check=True)
            
        else:
            print(f"Unsupported platform: {current_platform}")
            print("Supported platforms: Windows, macOS, Linux")
            sys.exit(1)
            
        print()
        print("=" * 60)
        print("Build completed successfully!")
        print("=" * 60)
        print()
        print("Next steps:")
        print("1. Test the executable in the release/ directory")
        print("2. Review the generated documentation")
        print("3. Distribute the release package")
        print()
        print("Files generated:")
        release_dir = Path("release")
        if release_dir.exists():
            for file_path in release_dir.iterdir():
                print(f"  - {file_path.name}")
        
    except subprocess.CalledProcessError as e:
        print(f"Build failed with exit code {e.returncode}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\nBuild interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

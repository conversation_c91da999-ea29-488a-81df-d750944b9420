#!/usr/bin/env python3
"""Test script for Excel overhaul import functionality with strict column filtering."""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from datetime import date, timedelta
import tempfile
from excel_importer import validate_overhaul_excel_columns, import_overhaul_data_from_excel
from models import Equipment, Overhaul

def create_test_excel_files():
    """Create test Excel files for validation testing."""
    test_files = {}
    
    # Test 1: Valid Excel file with both OH-I and OH-II columns
    valid_data = {
        'Equipment Name': ['Tank A', 'Vehicle B', 'Generator C'],
        'BA Number': ['BA001', 'BA002', 'BA003'],
        'Serial Number': ['SN001', 'SN002', 'SN003'],
        'OH-I': ['2020-01-15', '2019-06-20', ''],
        'OH-II': ['', '2023-08-10', '2021-12-05'],
        'Meterage': [45000, 38000, 25000]
    }
    
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
        df = pd.DataFrame(valid_data)
        df.to_excel(f.name, index=False, sheet_name='Equipment_Data')
        test_files['valid'] = f.name
    
    # Test 2: Invalid Excel file - missing OH-II column
    invalid_data_1 = {
        'Equipment Name': ['Tank A', 'Vehicle B'],
        'BA Number': ['BA001', 'BA002'],
        'OH-I': ['2020-01-15', '2019-06-20'],
        'Overhaul_2': ['', '2023-08-10']  # Wrong column name
    }
    
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
        df = pd.DataFrame(invalid_data_1)
        df.to_excel(f.name, index=False, sheet_name='Equipment_Data')
        test_files['invalid_missing_oh2'] = f.name
    
    # Test 3: Invalid Excel file - missing OH-I column
    invalid_data_2 = {
        'Equipment Name': ['Tank A', 'Vehicle B'],
        'BA Number': ['BA001', 'BA002'],
        'First_Overhaul': ['2020-01-15', '2019-06-20'],  # Wrong column name
        'OH-II': ['', '2023-08-10']
    }
    
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
        df = pd.DataFrame(invalid_data_2)
        df.to_excel(f.name, index=False, sheet_name='Equipment_Data')
        test_files['invalid_missing_oh1'] = f.name
    
    # Test 4: Multiple sheets - some valid, some invalid
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
        with pd.ExcelWriter(f.name) as writer:
            # Valid sheet
            pd.DataFrame(valid_data).to_excel(writer, sheet_name='Valid_Sheet', index=False)
            
            # Invalid sheet
            pd.DataFrame(invalid_data_1).to_excel(writer, sheet_name='Invalid_Sheet', index=False)
            
            # Another valid sheet
            valid_data_2 = {
                'Equipment Type': ['Truck D', 'Crane E'],
                'BA Number': ['BA004', 'BA005'],
                'OH-I': ['2021-03-10', ''],
                'OH-II': ['2024-01-15', '2022-11-20']
            }
            pd.DataFrame(valid_data_2).to_excel(writer, sheet_name='Another_Valid', index=False)
        
        test_files['multiple_sheets'] = f.name
    
    return test_files

def test_excel_validation():
    """Test Excel file validation functionality."""
    print("🧪 Testing Excel Overhaul Import Validation")
    print("=" * 50)
    
    test_files = create_test_excel_files()
    
    try:
        # Test 1: Valid Excel file
        print("\n📋 Test 1: Valid Excel file with OH-I and OH-II columns")
        is_valid, all_sheets, message, valid_sheets = validate_overhaul_excel_columns(test_files['valid'])
        
        if is_valid and len(valid_sheets) == 1:
            print("✅ PASS: Valid file correctly identified")
            print(f"   Valid sheets: {valid_sheets}")
        else:
            print(f"❌ FAIL: Valid file not recognized. Valid: {is_valid}, Sheets: {valid_sheets}")
            return False
        
        # Test 2: Invalid Excel file - missing OH-II
        print("\n📋 Test 2: Invalid Excel file - missing OH-II column")
        is_valid, all_sheets, message, valid_sheets = validate_overhaul_excel_columns(test_files['invalid_missing_oh2'])
        
        if not is_valid and len(valid_sheets) == 0:
            print("✅ PASS: Invalid file correctly rejected")
            print(f"   Error message: {message[:100]}...")
        else:
            print(f"❌ FAIL: Invalid file not rejected. Valid: {is_valid}, Sheets: {valid_sheets}")
            return False
        
        # Test 3: Invalid Excel file - missing OH-I
        print("\n📋 Test 3: Invalid Excel file - missing OH-I column")
        is_valid, all_sheets, message, valid_sheets = validate_overhaul_excel_columns(test_files['invalid_missing_oh1'])
        
        if not is_valid and len(valid_sheets) == 0:
            print("✅ PASS: Invalid file correctly rejected")
        else:
            print(f"❌ FAIL: Invalid file not rejected. Valid: {is_valid}, Sheets: {valid_sheets}")
            return False
        
        # Test 4: Multiple sheets - mixed valid/invalid
        print("\n📋 Test 4: Multiple sheets with mixed validity")
        is_valid, all_sheets, message, valid_sheets = validate_overhaul_excel_columns(test_files['multiple_sheets'])
        
        if is_valid and len(valid_sheets) == 2:  # Should find 2 valid sheets
            print("✅ PASS: Mixed file correctly processed")
            print(f"   Valid sheets: {valid_sheets}")
            print(f"   Total sheets: {len(all_sheets)}")
        else:
            print(f"❌ FAIL: Mixed file not processed correctly. Valid: {is_valid}, Valid sheets: {len(valid_sheets)}")
            return False
        
        print("\n🎉 All validation tests passed!")
        return True
        
    except Exception as e:
        print(f"💥 Test failed with error: {e}")
        return False
    
    finally:
        # Cleanup test files
        for file_path in test_files.values():
            try:
                os.unlink(file_path)
            except:
                pass

def test_column_name_strictness():
    """Test strict column name matching."""
    print("\n🔍 Testing Column Name Strictness")
    print("=" * 40)
    
    # Test case-sensitive matching
    test_cases = [
        (['OH-I', 'OH-II'], True, "Exact match"),
        (['oh-i', 'oh-ii'], False, "Lowercase"),
        (['OH-1', 'OH-2'], False, "Numbers instead of Roman"),
        (['OH-I ', ' OH-II'], True, "With spaces (should be trimmed)"),
        (['Overhaul-I', 'Overhaul-II'], False, "Different prefix"),
        (['OH-I', 'OH-II', 'Extra Column'], True, "Extra columns allowed"),
        (['OH-I'], False, "Missing OH-II"),
        (['OH-II'], False, "Missing OH-I"),
    ]
    
    for columns, expected, description in test_cases:
        # Create temporary test data
        test_data = {col: ['test'] for col in columns}

        # Create temporary file
        temp_file = tempfile.mktemp(suffix='.xlsx')
        try:
            df = pd.DataFrame(test_data)
            df.to_excel(temp_file, index=False)

            is_valid, _, _, valid_sheets = validate_overhaul_excel_columns(temp_file)
            result = len(valid_sheets) > 0

            if result == expected:
                print(f"✅ {description}: {'VALID' if expected else 'INVALID'} (as expected)")
            else:
                print(f"❌ {description}: Expected {'VALID' if expected else 'INVALID'}, got {'VALID' if result else 'INVALID'}")
                return False

        finally:
            try:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
            except:
                pass
    
    print("✅ All column name strictness tests passed!")
    return True

def test_integration_with_equipment():
    """Test integration with existing equipment data."""
    print("\n🔗 Testing Integration with Equipment Database")
    print("=" * 45)
    
    try:
        # Create test equipment
        test_equipment = Equipment(
            serial_number="TEST-IMPORT-001",
            make_and_type="Test Import Vehicle",
            ba_number="IMPORT123",
            units_held=1,
            vintage_years=10.0,
            meterage_kms=45000,
            date_of_commission=(date.today() - timedelta(days=365*10)).isoformat(),
            is_active=True,
            remarks="Test equipment for import testing"
        )
        
        eq_id = test_equipment.save()
        if not eq_id:
            print("❌ Failed to create test equipment")
            return False
        
        print(f"✅ Created test equipment with ID: {eq_id}")
        
        # Create Excel file with matching equipment
        import_data = {
            'Equipment Name': ['Test Import Vehicle'],
            'BA Number': ['IMPORT123'],
            'Serial Number': ['TEST-IMPORT-001'],
            'OH-I': ['2022-06-15'],
            'OH-II': [''],
            'Meterage': [45000]
        }
        
        temp_file = tempfile.mktemp(suffix='.xlsx')
        try:
            df = pd.DataFrame(import_data)
            df.to_excel(temp_file, index=False, sheet_name='Test_Import')

            # Test import
            result = import_overhaul_data_from_excel(temp_file)

            if result.get('success') and result.get('overhauls', 0) > 0:
                print("✅ Import successful")
                print(f"   Equipment processed: {result.get('equipment', 0)}")
                print(f"   Overhauls created: {result.get('overhauls', 0)}")

                # Verify overhaul record was created
                overhauls = Overhaul.get_by_equipment(eq_id)
                oh1_record = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-I'), None)

                if oh1_record and oh1_record.get('done_date'):
                    print("✅ OH-I record created with completion date")
                    return True
                else:
                    print("❌ OH-I record not found or missing completion date")
                    return False
            else:
                print(f"❌ Import failed: {result.get('error', 'Unknown error')}")
                return False
        finally:
            try:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
            except:
                pass
                
    except Exception as e:
        print(f"💥 Integration test failed: {e}")
        return False
    
    finally:
        # Cleanup
        try:
            if 'eq_id' in locals():
                # Delete overhaul records
                overhauls = Overhaul.get_by_equipment(eq_id)
                for overhaul in overhauls:
                    Overhaul.delete_by_id(overhaul['overhaul_id'])
                
                # Delete equipment
                Equipment.delete_by_id(eq_id)
                print("✅ Test data cleaned up")
        except Exception as e:
            print(f"⚠️  Warning: Failed to clean up test data: {e}")

if __name__ == "__main__":
    print("🚀 Excel Overhaul Import Filter Testing")
    print("=" * 60)
    
    success = True
    
    # Run all tests
    success &= test_excel_validation()
    success &= test_column_name_strictness()
    success &= test_integration_with_equipment()
    
    if success:
        print("\n🎉 All tests passed! Excel overhaul import filtering is working correctly!")
        print("\n📋 Summary of implemented features:")
        print("   ✅ Strict column name validation ('OH-I' and 'OH-II' exactly)")
        print("   ✅ Multi-sheet support with filtering")
        print("   ✅ Equipment matching by BA Number and Serial Number")
        print("   ✅ Overhaul record creation and updating")
        print("   ✅ Comprehensive error handling and validation")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
    
    sys.exit(0 if success else 1)

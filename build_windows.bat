@echo off
REM Army Equipment Inventory System - Windows Build Script
REM This script creates a Windows executable and installer package

echo ====================================================================
echo  Army Equipment Inventory System - Windows Build Script
echo ====================================================================
echo.

REM Set build configuration
set APP_NAME=ArmyInventorySystem
set APP_VERSION=2.0.0
set BUILD_DIR=build_windows
set DIST_DIR=dist_windows

REM Check Python installation
echo [1/8] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.9+ and try again.
    pause
    exit /b 1
)

REM Check PyInstaller installation
echo [2/8] Checking PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo Installing PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ERROR: Failed to install PyInstaller
        pause
        exit /b 1
    )
)

REM Install dependencies
echo [3/8] Installing dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

REM Clean previous builds
echo [4/8] Cleaning previous builds...
if exist "%BUILD_DIR%" rmdir /s /q "%BUILD_DIR%"
if exist "%DIST_DIR%" rmdir /s /q "%DIST_DIR%"
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"

REM Create app icon if it doesn't exist
echo [5/8] Preparing resources...
if not exist "resources\app_icon.ico" (
    echo Creating default app icon...
    if exist "resources\app_icon.svg" (
        REM Convert SVG to ICO if possible
        echo Note: SVG icon found but ICO conversion requires additional tools
    )
)

REM Build executable with PyInstaller
echo [6/8] Building executable...
echo This may take several minutes...
pyinstaller ArmyInventory.spec --clean --noconfirm --distpath "%DIST_DIR%" --workpath "%BUILD_DIR%"
if errorlevel 1 (
    echo ERROR: PyInstaller build failed
    pause
    exit /b 1
)

REM Verify executable was created
if not exist "%DIST_DIR%\%APP_NAME%.exe" (
    echo ERROR: Executable not found after build
    pause
    exit /b 1
)

REM Test the executable
echo [7/8] Testing executable...
echo Testing if the executable runs without errors...
timeout /t 2 >nul
"%DIST_DIR%\%APP_NAME%.exe" --version >nul 2>&1
if errorlevel 1 (
    echo WARNING: Executable test failed, but continuing...
)

REM Create installer (if NSIS is available)
echo [8/8] Creating installer...
where makensis >nul 2>&1
if errorlevel 1 (
    echo NSIS not found. Skipping installer creation.
    echo You can install NSIS from https://nsis.sourceforge.io/
    echo Then run: makensis installer_windows.nsi
) else (
    echo Creating Windows installer...
    makensis installer_windows.nsi
    if errorlevel 1 (
        echo WARNING: Installer creation failed
    ) else (
        echo Installer created successfully!
    )
)

REM Create distribution package
echo.
echo Creating distribution package...
if not exist "release" mkdir "release"

REM Copy executable to release directory
copy "%DIST_DIR%\%APP_NAME%.exe" "release\"

REM Copy documentation
copy "README.md" "release\"
copy "LICENSE.txt" "release\"

REM Create run script
echo @echo off > "release\run_%APP_NAME%.bat"
echo echo Starting %APP_NAME%... >> "release\run_%APP_NAME%.bat"
echo %APP_NAME%.exe >> "release\run_%APP_NAME%.bat"
echo if errorlevel 1 pause >> "release\run_%APP_NAME%.bat"

REM Create README for users
echo %APP_NAME% - Windows Distribution > "release\README_WINDOWS.txt"
echo. >> "release\README_WINDOWS.txt"
echo This package contains the standalone Windows executable for %APP_NAME%. >> "release\README_WINDOWS.txt"
echo. >> "release\README_WINDOWS.txt"
echo To run the application: >> "release\README_WINDOWS.txt"
echo 1. Double-click on %APP_NAME%.exe >> "release\README_WINDOWS.txt"
echo    OR >> "release\README_WINDOWS.txt"
echo 2. Double-click on run_%APP_NAME%.bat >> "release\README_WINDOWS.txt"
echo. >> "release\README_WINDOWS.txt"
echo No additional installation required. >> "release\README_WINDOWS.txt"
echo All dependencies are bundled within the executable. >> "release\README_WINDOWS.txt"
echo. >> "release\README_WINDOWS.txt"
echo System Requirements: >> "release\README_WINDOWS.txt"
echo - Windows 7, 8, 10, or 11 >> "release\README_WINDOWS.txt"
echo - 4GB RAM minimum (8GB recommended) >> "release\README_WINDOWS.txt"
echo - 200MB free disk space >> "release\README_WINDOWS.txt"

echo.
echo ====================================================================
echo  Build completed successfully!
echo ====================================================================
echo.
echo Executable location: %DIST_DIR%\%APP_NAME%.exe
echo Distribution package: release\
echo.
if exist "ArmyInventorySystem_Setup.exe" (
    echo Installer: ArmyInventorySystem_Setup.exe
)
echo.
echo You can now distribute the files in the 'release' directory.
echo.
pause

#!/bin/bash
# Army Equipment Inventory System - macOS Build Script
# This script creates a macOS application bundle and DMG installer

set -e  # Exit on any error

APP_NAME="ArmyInventorySystem"
APP_VERSION="2.0.0"
BUILD_DIR="build_macos"
DIST_DIR="dist_macos"
DMG_NAME="ArmyInventorySystem_${APP_VERSION}_macOS"

echo "===================================================================="
echo " Army Equipment Inventory System - macOS Build Script"
echo "===================================================================="
echo

# Check Python installation
echo "[1/8] Checking Python installation..."
if ! command -v python3 &> /dev/null; then
    echo "ERROR: Python 3 is not installed"
    echo "Please install Python 3.9+ and try again."
    exit 1
fi

python3 --version

# Check PyInstaller installation
echo "[2/8] Checking PyInstaller..."
if ! python3 -c "import PyInstaller" &> /dev/null; then
    echo "Installing PyInstaller..."
    pip3 install pyinstaller
fi

# Install dependencies
echo "[3/8] Installing dependencies..."
pip3 install -r requirements.txt

# Clean previous builds
echo "[4/8] Cleaning previous builds..."
rm -rf "$BUILD_DIR" "$DIST_DIR" "build" "dist"

# Create app icon if it doesn't exist
echo "[5/8] Preparing resources..."
if [ ! -f "resources/app_icon.icns" ]; then
    echo "Creating app icon..."
    if [ -f "resources/app_icon.svg" ]; then
        # Convert SVG to ICNS if possible
        if command -v rsvg-convert &> /dev/null && command -v iconutil &> /dev/null; then
            echo "Converting SVG to ICNS..."
            mkdir -p "temp_iconset.iconset"
            
            # Create different sizes for iconset
            for size in 16 32 64 128 256 512 1024; do
                rsvg-convert -w $size -h $size "resources/app_icon.svg" -o "temp_iconset.iconset/icon_${size}x${size}.png"
                if [ $size -le 512 ]; then
                    rsvg-convert -w $((size*2)) -h $((size*2)) "resources/app_icon.svg" -o "temp_iconset.iconset/icon_${size}x${size}@2x.png"
                fi
            done
            
            iconutil -c icns "temp_iconset.iconset" -o "resources/app_icon.icns"
            rm -rf "temp_iconset.iconset"
        else
            echo "Note: SVG icon found but conversion tools not available"
            echo "Install librsvg and use iconutil to convert SVG to ICNS"
        fi
    fi
fi

# Build application bundle with PyInstaller
echo "[6/8] Building application bundle..."
echo "This may take several minutes..."
python3 -m PyInstaller ArmyInventory.spec --clean --noconfirm --distpath "$DIST_DIR" --workpath "$BUILD_DIR"

# Verify app bundle was created
if [ ! -d "$DIST_DIR/${APP_NAME}.app" ]; then
    echo "ERROR: Application bundle not found after build"
    exit 1
fi

# Test the application bundle
echo "[7/8] Testing application bundle..."
echo "Testing if the application bundle is valid..."
if ! "$DIST_DIR/${APP_NAME}.app/Contents/MacOS/${APP_NAME}" --version &> /dev/null; then
    echo "WARNING: Application test failed, but continuing..."
fi

# Create DMG installer
echo "[8/8] Creating DMG installer..."

# Create temporary DMG directory
DMG_TEMP_DIR="dmg_temp"
rm -rf "$DMG_TEMP_DIR"
mkdir "$DMG_TEMP_DIR"

# Copy app bundle to DMG directory
cp -R "$DIST_DIR/${APP_NAME}.app" "$DMG_TEMP_DIR/"

# Copy documentation
cp "README.md" "$DMG_TEMP_DIR/"
cp "LICENSE.txt" "$DMG_TEMP_DIR/"

# Create Applications symlink for drag-and-drop installation
ln -s /Applications "$DMG_TEMP_DIR/Applications"

# Create DMG background and styling (if available)
if [ -f "resources/dmg_background.png" ]; then
    cp "resources/dmg_background.png" "$DMG_TEMP_DIR/.background.png"
fi

# Create the DMG
echo "Creating DMG installer..."
if command -v create-dmg &> /dev/null; then
    # Use create-dmg if available (brew install create-dmg)
    create-dmg \
        --volname "${APP_NAME}" \
        --volicon "resources/app_icon.icns" \
        --window-pos 200 120 \
        --window-size 800 600 \
        --icon-size 100 \
        --icon "${APP_NAME}.app" 200 190 \
        --hide-extension "${APP_NAME}.app" \
        --app-drop-link 600 185 \
        "${DMG_NAME}.dmg" \
        "$DMG_TEMP_DIR"
else
    # Fallback to hdiutil
    echo "Using hdiutil to create DMG..."
    hdiutil create -volname "${APP_NAME}" -srcfolder "$DMG_TEMP_DIR" -ov -format UDZO "${DMG_NAME}.dmg"
fi

# Clean up temporary directory
rm -rf "$DMG_TEMP_DIR"

# Create distribution package
echo
echo "Creating distribution package..."
mkdir -p "release"

# Copy app bundle to release directory
cp -R "$DIST_DIR/${APP_NAME}.app" "release/"

# Copy DMG to release directory
if [ -f "${DMG_NAME}.dmg" ]; then
    cp "${DMG_NAME}.dmg" "release/"
fi

# Copy documentation
cp "README.md" "release/"
cp "LICENSE.txt" "release/"

# Create README for users
cat > "release/README_MACOS.txt" << EOF
${APP_NAME} - macOS Distribution

This package contains the macOS application bundle for ${APP_NAME}.

Installation Options:

1. DMG Installer (Recommended):
   - Double-click on ${DMG_NAME}.dmg
   - Drag ${APP_NAME}.app to the Applications folder
   - Eject the DMG
   - Launch from Applications or Launchpad

2. Direct Installation:
   - Copy ${APP_NAME}.app to your Applications folder
   - Launch from Applications or Launchpad

System Requirements:
- macOS 10.14 (Mojave) or later
- 4GB RAM minimum (8GB recommended)
- 200MB free disk space

Security Note:
If you see a security warning when first launching:
1. Go to System Preferences > Security & Privacy
2. Click "Open Anyway" for ${APP_NAME}
3. Or right-click the app and select "Open"

No additional installation required.
All dependencies are bundled within the application.
EOF

echo
echo "===================================================================="
echo " Build completed successfully!"
echo "===================================================================="
echo
echo "Application bundle: $DIST_DIR/${APP_NAME}.app"
if [ -f "${DMG_NAME}.dmg" ]; then
    echo "DMG installer: ${DMG_NAME}.dmg"
fi
echo "Distribution package: release/"
echo
echo "You can now distribute the files in the 'release' directory."
echo

# Make the script executable
chmod +x "$0"

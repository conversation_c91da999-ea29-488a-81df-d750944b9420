#!/usr/bin/env python3
"""Test script for overhaul status calculation logic."""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, timedelta
import utils

def test_overhaul_status_calculation():
    """Test the overhaul status calculation with various scenarios."""
    
    print("🧪 Testing Overhaul Status Calculation Logic")
    print("=" * 50)
    
    today = date.today()
    
    # Test scenarios
    test_cases = [
        # OH-I Date-based tests
        {
            "name": "OH-I: Scheduled (2+ years to due)",
            "overhaul_type": "OH-I",
            "due_date": today + timedelta(days=800),
            "done_date": None,
            "meterage_km": 30000,
            "expected": "scheduled"
        },
        {
            "name": "OH-I: Warning (6 months to due)",
            "overhaul_type": "OH-I", 
            "due_date": today + timedelta(days=180),
            "done_date": None,
            "meterage_km": 30000,
            "expected": "warning"
        },
        {
            "name": "OH-I: Critical (15 days to due)",
            "overhaul_type": "OH-I",
            "due_date": today + timedelta(days=15),
            "done_date": None,
            "meterage_km": 30000,
            "expected": "critical"
        },
        {
            "name": "OH-I: Overdue (past due date)",
            "overhaul_type": "OH-I",
            "due_date": today - timedelta(days=30),
            "done_date": None,
            "meterage_km": 30000,
            "expected": "overdue"
        },
        
        # OH-I Mileage-based tests
        {
            "name": "OH-I: Warning by mileage (52K KM)",
            "overhaul_type": "OH-I",
            "due_date": today + timedelta(days=800),
            "done_date": None,
            "meterage_km": 52000,
            "expected": "warning"
        },
        {
            "name": "OH-I: Critical by mileage (57K KM)",
            "overhaul_type": "OH-I",
            "due_date": today + timedelta(days=800),
            "done_date": None,
            "meterage_km": 57000,
            "expected": "critical"
        },
        {
            "name": "OH-I: Overdue by mileage (62K KM)",
            "overhaul_type": "OH-I",
            "due_date": today + timedelta(days=800),
            "done_date": None,
            "meterage_km": 62000,
            "expected": "overdue"
        },
        
        # Completion tests
        {
            "name": "OH-I: Completed",
            "overhaul_type": "OH-I",
            "due_date": today - timedelta(days=30),
            "done_date": today - timedelta(days=10),
            "meterage_km": 65000,
            "expected": "completed"
        },
        {
            "name": "OH-II: Completed",
            "overhaul_type": "OH-II",
            "due_date": today - timedelta(days=30),
            "done_date": today - timedelta(days=10),
            "meterage_km": 70000,
            "expected": "completed"
        },
        
        # OH-II Discard test
        {
            "name": "OH-II: Discard (10+ years after completion)",
            "overhaul_type": "OH-II",
            "due_date": today - timedelta(days=4000),
            "done_date": today - timedelta(days=3700),  # ~10 years ago
            "meterage_km": 80000,
            "expected": "discard"
        },
        
        # OH-II Date-based tests
        {
            "name": "OH-II: Scheduled (2+ years to due)",
            "overhaul_type": "OH-II",
            "due_date": today + timedelta(days=800),
            "done_date": None,
            "meterage_km": 70000,
            "expected": "scheduled"
        },
        {
            "name": "OH-II: Critical (20 days to due)",
            "overhaul_type": "OH-II",
            "due_date": today + timedelta(days=20),
            "done_date": None,
            "meterage_km": 70000,
            "expected": "critical"
        }
    ]
    
    passed = 0
    failed = 0
    
    for test_case in test_cases:
        try:
            result = utils.calculate_overhaul_status(
                overhaul_type=test_case["overhaul_type"],
                due_date=test_case["due_date"],
                done_date=test_case["done_date"],
                meterage_km=test_case["meterage_km"]
            )
            
            expected = test_case["expected"]
            
            if result == expected:
                print(f"✅ {test_case['name']}: {result}")
                passed += 1
            else:
                print(f"❌ {test_case['name']}: Expected '{expected}', got '{result}'")
                failed += 1
                
        except Exception as e:
            print(f"💥 {test_case['name']}: Error - {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Status calculation is working correctly.")
    else:
        print("⚠️  Some tests failed. Status calculation needs fixes.")
    
    return failed == 0

def test_status_colors():
    """Test status color mapping."""
    print("\n🎨 Testing Status Color Mapping")
    print("=" * 30)
    
    statuses = ["scheduled", "warning", "critical", "overdue", "completed", "discard", "unknown"]
    
    for status in statuses:
        color = utils.get_status_color(status)
        print(f"📍 {status.upper()}: {color}")
    
    print("✅ Color mapping test completed")

if __name__ == "__main__":
    success = test_overhaul_status_calculation()
    test_status_colors()
    
    if success:
        print("\n🚀 Overhaul status logic is working correctly!")
    else:
        print("\n🔧 Overhaul status logic needs debugging.")
    
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""Test script for equipment creation with automatic overhaul record generation."""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, timedelta
from models import Equipment, Overhaul
import overhaul_service
import database

def test_equipment_overhaul_creation():
    """Test the equipment creation with automatic overhaul record generation."""
    
    print("🧪 Testing Equipment Creation with Overhaul Records")
    print("=" * 55)
    
    try:
        # Test data - use equipment that's close to OH-I due date
        test_commission_date = date.today() - timedelta(days=365*14)  # 14 years ago (close to 15-year OH-I)
        test_meterage = 55000  # 55,000 KM (close to 60K limit)

        # Create test equipment
        test_equipment = Equipment(
            serial_number="TEST-001",
            make_and_type="Test Vehicle Type",
            ba_number="TEST123A",
            units_held=1,
            vintage_years=14.0,  # Match the commission date
            meterage_kms=test_meterage,
            meterage_description="Test meterage",
            date_of_commission=test_commission_date.isoformat(),
            is_active=True,
            remarks="Test equipment for overhaul creation"
        )
        
        # Save equipment
        equipment_id = test_equipment.save()
        
        if not equipment_id:
            print("❌ Failed to create test equipment")
            return False
            
        print(f"✅ Created test equipment with ID: {equipment_id}")
        print(f"   Commission Date: {test_commission_date}")
        print(f"   Meterage: {test_meterage:,} KM")
        
        # Test overhaul record creation
        success = overhaul_service.create_equipment_overhauls(
            equipment_id=equipment_id,
            date_of_commission=test_commission_date.isoformat(),
            meterage_kms=test_meterage
        )
        
        if not success:
            print("❌ Failed to create overhaul records")
            return False
            
        print("✅ Overhaul record creation completed")
        
        # Verify overhaul records
        overhauls = Overhaul.get_by_equipment(equipment_id)
        
        if not overhauls:
            print("❌ No overhaul records found")
            return False
            
        print(f"✅ Found {len(overhauls)} overhaul record(s)")
        
        # Check OH-I record
        oh1 = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-I'), None)
        oh2 = next((oh for oh in overhauls if oh.get('overhaul_type') == 'OH-II'), None)
        
        if not oh1:
            print("❌ OH-I record not found")
            return False
            
        print("✅ OH-I record found:")
        print(f"   Due Date: {oh1.get('due_date')}")
        print(f"   Status: {oh1.get('status')}")
        print(f"   Done Date: {oh1.get('done_date') or 'Not completed'}")
        
        # Verify OH-I due date calculation (should be 15 years from commission)
        expected_oh1_due = overhaul_service.calculate_oh1_due_date(test_commission_date)
        actual_oh1_due = date.fromisoformat(oh1.get('due_date'))

        if expected_oh1_due and abs((expected_oh1_due - actual_oh1_due).days) <= 1:  # Allow 1 day difference
            print("✅ OH-I due date calculated correctly")
        else:
            print(f"❌ OH-I due date incorrect. Expected: {expected_oh1_due}, Got: {actual_oh1_due}")
            return False
        
        # Verify OH-II record should NOT exist initially
        if oh2:
            print("❌ OH-II record should not exist for new equipment")
            return False
        else:
            print("✅ OH-II record correctly not created (will be created when OH-I is completed)")
        
        # Test status calculation
        expected_status = overhaul_service.get_overhaul_status(
            'OH-I', actual_oh1_due, None,
            date_of_commission=test_commission_date,
            meterage_km=test_meterage
        )
        
        if oh1.get('status') == expected_status:
            print(f"✅ OH-I status calculated correctly: {expected_status}")
        else:
            print(f"❌ OH-I status incorrect. Expected: {expected_status}, Got: {oh1.get('status')}")
            return False
        
        # Test completion workflow
        print("\n🔄 Testing OH-I completion workflow...")

        # Use a completion date that's within the allowed window (within 1 year of due date)
        oh1_due_date = overhaul_service.calculate_oh1_due_date(test_commission_date)
        completion_date = oh1_due_date - timedelta(days=30)  # Complete 30 days before due
        try:
            overhaul_service.complete_overhaul(
                equipment_id=equipment_id,
                overhaul_type='OH-I',
                completion_date=completion_date.isoformat(),
                completion_notes="Test completion"
            )
            print("✅ OH-I completion successful")
            
            # Check if OH-II record was created
            updated_overhauls = Overhaul.get_by_equipment(equipment_id)
            oh2_after_completion = next((oh for oh in updated_overhauls if oh.get('overhaul_type') == 'OH-II'), None)
            
            if oh2_after_completion:
                print("✅ OH-II record created after OH-I completion")
                print(f"   OH-II Due Date: {oh2_after_completion.get('due_date')}")
                
                # Verify OH-II due date (should be 10 years after OH-I completion)
                expected_oh2_due = overhaul_service.calculate_oh2_due_date(completion_date)
                actual_oh2_due = date.fromisoformat(oh2_after_completion.get('due_date'))

                if expected_oh2_due and abs((expected_oh2_due - actual_oh2_due).days) <= 1:
                    print("✅ OH-II due date calculated correctly")
                else:
                    print(f"❌ OH-II due date incorrect. Expected: {expected_oh2_due}, Got: {actual_oh2_due}")
                    return False
            else:
                print("❌ OH-II record not created after OH-I completion")
                return False
                
        except Exception as e:
            print(f"❌ OH-I completion failed: {e}")
            return False
        
        # Cleanup test data
        print("\n🧹 Cleaning up test data...")
        try:
            # Delete overhaul records
            for overhaul in Overhaul.get_by_equipment(equipment_id):
                Overhaul.delete_by_id(overhaul['overhaul_id'])
            
            # Delete equipment
            Equipment.delete_by_id(equipment_id)
            print("✅ Test data cleaned up")
            
        except Exception as e:
            print(f"⚠️  Warning: Failed to clean up test data: {e}")
        
        print("\n🎉 All tests passed! Equipment overhaul creation is working correctly.")
        return True
        
    except Exception as e:
        print(f"💥 Test failed with error: {e}")
        return False

if __name__ == "__main__":
    success = test_equipment_overhaul_creation()
    
    if success:
        print("\n✅ Equipment overhaul creation implementation is working correctly!")
    else:
        print("\n❌ Equipment overhaul creation implementation needs fixes.")
    
    sys.exit(0 if success else 1)

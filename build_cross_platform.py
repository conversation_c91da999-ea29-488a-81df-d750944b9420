#!/usr/bin/env python3
"""
Cross-platform build script for Army Equipment Inventory System
Handles building executables and installers for Windows and macOS
"""

import os
import sys
import subprocess
import platform
import shutil
import hashlib
from pathlib import Path

# Build configuration
APP_NAME = "ArmyInventorySystem"
APP_VERSION = "2.0.0"
APP_DESCRIPTION = "Army Equipment Inventory Management System"

class CrossPlatformBuilder:
    def __init__(self):
        self.platform = platform.system().lower()
        self.build_dir = Path(f"build_{self.platform}")
        self.dist_dir = Path(f"dist_{self.platform}")
        self.release_dir = Path("release")
        
    def log(self, message):
        """Print timestamped log message"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
        
    def run_command(self, command, check=True):
        """Run shell command with error handling"""
        self.log(f"Running: {command}")
        try:
            if isinstance(command, str):
                result = subprocess.run(command, shell=True, check=check, 
                                      capture_output=True, text=True)
            else:
                result = subprocess.run(command, check=check, 
                                      capture_output=True, text=True)
            
            if result.stdout:
                print(result.stdout)
            if result.stderr and result.returncode != 0:
                print(f"Error: {result.stderr}")
                
            return result
        except subprocess.CalledProcessError as e:
            self.log(f"Command failed: {e}")
            if e.stdout:
                print(f"stdout: {e.stdout}")
            if e.stderr:
                print(f"stderr: {e.stderr}")
            raise
            
    def check_dependencies(self):
        """Check if required dependencies are installed"""
        self.log("Checking dependencies...")
        
        # Check Python version
        if sys.version_info < (3, 9):
            raise RuntimeError("Python 3.9 or higher is required")
            
        # Check PyInstaller
        try:
            import PyInstaller
            self.log(f"PyInstaller version: {PyInstaller.__version__}")
        except ImportError:
            self.log("Installing PyInstaller...")
            self.run_command([sys.executable, "-m", "pip", "install", "pyinstaller"])
            
        # Install requirements
        self.log("Installing requirements...")
        self.run_command([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
    def clean_build_dirs(self):
        """Clean previous build directories"""
        self.log("Cleaning build directories...")
        
        dirs_to_clean = [
            self.build_dir,
            self.dist_dir,
            Path("build"),
            Path("dist"),
            Path("__pycache__"),
        ]
        
        for dir_path in dirs_to_clean:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                self.log(f"Removed {dir_path}")
                
    def prepare_resources(self):
        """Prepare resources for building"""
        self.log("Preparing resources...")
        
        # Create resources directory if it doesn't exist
        resources_dir = Path("resources")
        resources_dir.mkdir(exist_ok=True)
        
        # Create version file for Windows
        if self.platform == "windows":
            self.log("Creating version file...")
            self.run_command([sys.executable, "version_info.py"])
            
        # Check for icon files
        if self.platform == "windows":
            icon_file = resources_dir / "app_icon.ico"
            if not icon_file.exists():
                self.log("Warning: app_icon.ico not found, executable will use default icon")
        elif self.platform == "darwin":
            icon_file = resources_dir / "app_icon.icns"
            if not icon_file.exists():
                self.log("Warning: app_icon.icns not found, app bundle will use default icon")
                
    def build_executable(self):
        """Build the executable using PyInstaller"""
        self.log("Building executable...")
        
        # Build command
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "ArmyInventory.spec",
            "--clean",
            "--noconfirm",
            f"--distpath={self.dist_dir}",
            f"--workpath={self.build_dir}"
        ]
        
        self.run_command(cmd)
        
        # Verify build
        if self.platform == "windows":
            exe_path = self.dist_dir / f"{APP_NAME}.exe"
            if not exe_path.exists():
                raise RuntimeError("Windows executable not found after build")
        elif self.platform == "darwin":
            app_path = self.dist_dir / f"{APP_NAME}.app"
            if not app_path.exists():
                raise RuntimeError("macOS app bundle not found after build")
                
        self.log("Executable built successfully!")
        
    def test_executable(self):
        """Test the built executable"""
        self.log("Testing executable...")
        
        if self.platform == "windows":
            exe_path = self.dist_dir / f"{APP_NAME}.exe"
            # Test if executable can be launched (will exit quickly)
            try:
                result = self.run_command([str(exe_path), "--version"], check=False)
                if result.returncode == 0:
                    self.log("Executable test passed")
                else:
                    self.log("Warning: Executable test returned non-zero exit code")
            except Exception as e:
                self.log(f"Warning: Could not test executable: {e}")
                
        elif self.platform == "darwin":
            app_path = self.dist_dir / f"{APP_NAME}.app"
            exe_path = app_path / "Contents" / "MacOS" / APP_NAME
            try:
                result = self.run_command([str(exe_path), "--version"], check=False)
                if result.returncode == 0:
                    self.log("App bundle test passed")
                else:
                    self.log("Warning: App bundle test returned non-zero exit code")
            except Exception as e:
                self.log(f"Warning: Could not test app bundle: {e}")
                
    def create_installer(self):
        """Create platform-specific installer"""
        self.log("Creating installer...")
        
        if self.platform == "windows":
            self._create_windows_installer()
        elif self.platform == "darwin":
            self._create_macos_installer()
        else:
            self.log(f"Installer creation not implemented for {self.platform}")
            
    def _create_windows_installer(self):
        """Create Windows NSIS installer"""
        # Check if NSIS is available
        try:
            self.run_command("makensis", check=False)
            self.log("Creating NSIS installer...")
            self.run_command("makensis installer_windows.nsi")
            self.log("Windows installer created successfully!")
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.log("NSIS not found. Skipping installer creation.")
            self.log("Install NSIS from https://nsis.sourceforge.io/ to create installers")
            
    def _create_macos_installer(self):
        """Create macOS DMG installer"""
        dmg_name = f"{APP_NAME}_{APP_VERSION}_macOS.dmg"
        app_path = self.dist_dir / f"{APP_NAME}.app"
        
        # Create temporary DMG directory
        dmg_temp = Path("dmg_temp")
        if dmg_temp.exists():
            shutil.rmtree(dmg_temp)
        dmg_temp.mkdir()
        
        try:
            # Copy app bundle
            shutil.copytree(app_path, dmg_temp / f"{APP_NAME}.app")
            
            # Copy documentation
            shutil.copy("README.md", dmg_temp)
            shutil.copy("LICENSE.txt", dmg_temp)
            
            # Create Applications symlink
            (dmg_temp / "Applications").symlink_to("/Applications")
            
            # Create DMG
            self.run_command([
                "hdiutil", "create",
                "-volname", APP_NAME,
                "-srcfolder", str(dmg_temp),
                "-ov", "-format", "UDZO",
                dmg_name
            ])
            
            self.log("macOS DMG installer created successfully!")
            
        finally:
            # Clean up
            if dmg_temp.exists():
                shutil.rmtree(dmg_temp)
                
    def create_release_package(self):
        """Create final release package"""
        self.log("Creating release package...")
        
        # Create release directory
        self.release_dir.mkdir(exist_ok=True)
        
        # Copy executable/app bundle
        if self.platform == "windows":
            exe_path = self.dist_dir / f"{APP_NAME}.exe"
            shutil.copy(exe_path, self.release_dir)
            
            # Create run script
            run_script = self.release_dir / f"run_{APP_NAME}.bat"
            with open(run_script, 'w') as f:
                f.write(f"@echo off\n")
                f.write(f"echo Starting {APP_NAME}...\n")
                f.write(f"{APP_NAME}.exe\n")
                f.write(f"if errorlevel 1 pause\n")
                
        elif self.platform == "darwin":
            app_path = self.dist_dir / f"{APP_NAME}.app"
            shutil.copytree(app_path, self.release_dir / f"{APP_NAME}.app")
            
        # Copy documentation
        docs = ["README.md", "LICENSE.txt", "INSTALLATION_GUIDE.md", 
                "TROUBLESHOOTING_GUIDE.md", "ANTIVIRUS_WHITELIST_GUIDE.md"]
        
        for doc in docs:
            if Path(doc).exists():
                shutil.copy(doc, self.release_dir)
                
        # Copy installer if it exists
        if self.platform == "windows":
            installer_path = Path("ArmyInventorySystem_Setup.exe")
            if installer_path.exists():
                shutil.copy(installer_path, self.release_dir)
        elif self.platform == "darwin":
            dmg_path = Path(f"{APP_NAME}_{APP_VERSION}_macOS.dmg")
            if dmg_path.exists():
                shutil.copy(dmg_path, self.release_dir)
                
        # Create platform-specific README
        self._create_platform_readme()
        
        self.log(f"Release package created in {self.release_dir}")
        
    def _create_platform_readme(self):
        """Create platform-specific README file"""
        readme_path = self.release_dir / f"README_{self.platform.upper()}.txt"
        
        with open(readme_path, 'w') as f:
            f.write(f"{APP_NAME} - {self.platform.title()} Distribution\n")
            f.write("=" * 50 + "\n\n")
            
            if self.platform == "windows":
                f.write("Installation Options:\n\n")
                f.write("1. Installer (Recommended):\n")
                f.write("   - Run ArmyInventorySystem_Setup.exe\n")
                f.write("   - Follow installation wizard\n\n")
                f.write("2. Portable:\n")
                f.write(f"   - Double-click {APP_NAME}.exe\n")
                f.write(f"   - Or use run_{APP_NAME}.bat\n\n")
                f.write("System Requirements:\n")
                f.write("- Windows 7, 8, 10, or 11 (64-bit)\n")
                f.write("- 4GB RAM minimum (8GB recommended)\n")
                f.write("- 200MB free disk space\n\n")
                
            elif self.platform == "darwin":
                f.write("Installation:\n\n")
                f.write("1. DMG Installer (Recommended):\n")
                f.write(f"   - Double-click {APP_NAME}_{APP_VERSION}_macOS.dmg\n")
                f.write(f"   - Drag {APP_NAME}.app to Applications\n\n")
                f.write("2. Direct Installation:\n")
                f.write(f"   - Copy {APP_NAME}.app to Applications folder\n\n")
                f.write("System Requirements:\n")
                f.write("- macOS 10.14 (Mojave) or later\n")
                f.write("- 4GB RAM minimum (8GB recommended)\n")
                f.write("- 200MB free disk space\n\n")
                
            f.write("No Python installation required.\n")
            f.write("All dependencies are bundled.\n\n")
            f.write("For detailed instructions, see INSTALLATION_GUIDE.md\n")
            f.write("For troubleshooting, see TROUBLESHOOTING_GUIDE.md\n")
            
    def calculate_checksums(self):
        """Calculate checksums for release files"""
        self.log("Calculating checksums...")
        
        checksum_file = self.release_dir / "checksums.txt"
        
        with open(checksum_file, 'w') as f:
            f.write(f"{APP_NAME} {APP_VERSION} - File Checksums\n")
            f.write("=" * 50 + "\n\n")
            
            for file_path in self.release_dir.iterdir():
                if file_path.is_file() and file_path.name != "checksums.txt":
                    sha256_hash = hashlib.sha256()
                    with open(file_path, "rb") as file:
                        for chunk in iter(lambda: file.read(4096), b""):
                            sha256_hash.update(chunk)
                    
                    f.write(f"{file_path.name}:\n")
                    f.write(f"  SHA256: {sha256_hash.hexdigest()}\n\n")
                    
        self.log("Checksums calculated and saved")
        
    def build(self):
        """Main build process"""
        self.log(f"Starting build for {self.platform}")
        
        try:
            self.check_dependencies()
            self.clean_build_dirs()
            self.prepare_resources()
            self.build_executable()
            self.test_executable()
            self.create_installer()
            self.create_release_package()
            self.calculate_checksums()
            
            self.log("Build completed successfully!")
            self.log(f"Release files available in: {self.release_dir}")
            
        except Exception as e:
            self.log(f"Build failed: {e}")
            raise

def main():
    """Main entry point"""
    builder = CrossPlatformBuilder()
    builder.build()

if __name__ == "__main__":
    main()

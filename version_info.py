"""
Version information for Windows executable
Used by PyInstaller to embed version info in the .exe file
"""

# Version information for Windows executable
version_info = {
    'version': (2, 0, 0, 0),
    'file_version': (2, 0, 0, 0),
    'product_version': (2, 0, 0, 0),
    'file_flags_mask': 0x3f,
    'file_flags': 0x0,
    'file_os': 0x40004,  # VOS_NT_WINDOWS32
    'file_type': 0x1,    # VFT_APP
    'file_subtype': 0x0,
    'string_info': [
        {
            'lang_id': 0x0409,  # English (US)
            'char_set': 0x04b0,  # Unicode
            'string_table': {
                'CompanyName': 'Army Equipment Management',
                'FileDescription': 'Army Equipment Inventory Management System',
                'FileVersion': '*******',
                'InternalName': 'ArmyInventorySystem',
                'LegalCopyright': '© 2024 Army Equipment Management',
                'OriginalFilename': 'ArmyInventorySystem.exe',
                'ProductName': 'Army Equipment Inventory System',
                'ProductVersion': '*******',
                'Comments': 'Comprehensive equipment inventory management for military operations',
            }
        }
    ]
}

# Create version file for PyInstaller
def create_version_file():
    """Create a version file for PyInstaller"""
    version_template = """
# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers={file_version},
    prodvers={product_version},
    mask={file_flags_mask:#x},
    flags={file_flags:#x},
    OS={file_os:#x},
    fileType={file_type:#x},
    subtype={file_subtype:#x},
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'{lang_id:04x}{char_set:04x}',
          {string_table}
        )
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [{lang_id:#x}, {char_set:#x}])])
  ]
)
"""
    
    # Format string table
    string_items = []
    for key, value in version_info['string_info'][0]['string_table'].items():
        string_items.append(f"[u'{key}', u'{value}']")
    
    string_table_formatted = "[\n            " + ",\n            ".join(string_items) + "\n          ]"
    
    # Format the template
    formatted_version = version_template.format(
        file_version=version_info['version'],
        product_version=version_info['product_version'],
        file_flags_mask=version_info['file_flags_mask'],
        file_flags=version_info['file_flags'],
        file_os=version_info['file_os'],
        file_type=version_info['file_type'],
        file_subtype=version_info['file_subtype'],
        lang_id=version_info['string_info'][0]['lang_id'],
        char_set=version_info['string_info'][0]['char_set'],
        string_table=string_table_formatted
    )
    
    # Write to file
    with open('version_file.txt', 'w', encoding='utf-8') as f:
        f.write(formatted_version)
    
    print("Version file created: version_file.txt")

if __name__ == "__main__":
    create_version_file()

# Army Equipment Inventory System - Installation Guide

## Overview

This guide provides comprehensive installation instructions for the Army Equipment Inventory System across Windows and macOS platforms. The application is packaged as a standalone executable that requires no additional Python installation.

## System Requirements

### Windows
- **Operating System**: Windows 7, 8, 10, or 11 (64-bit recommended)
- **Memory**: 4GB RAM minimum (8GB recommended)
- **Storage**: 200MB free disk space
- **Display**: 1024x768 minimum resolution (1920x1080 recommended)
- **Permissions**: Administrator rights for installation (not required for running)

### macOS
- **Operating System**: macOS 10.14 (Mojave) or later
- **Memory**: 4GB RAM minimum (8GB recommended)
- **Storage**: 200MB free disk space
- **Display**: 1024x768 minimum resolution (1920x1080 recommended)

## Windows Installation

### Option 1: Using the Installer (Recommended)

1. **Download the Installer**
   - Download `ArmyInventorySystem_Setup.exe`
   - Verify the file is not corrupted (check file size matches documentation)

2. **Run the Installer**
   - Right-click on `ArmyInventorySystem_Setup.exe`
   - Select "Run as administrator"
   - If Windows SmartScreen appears, click "More info" then "Run anyway"

3. **Installation Wizard**
   - Click "Next" on the welcome screen
   - Read and accept the license agreement
   - Choose installation components:
     - ✅ Core Application (required)
     - ✅ Desktop Shortcut (recommended)
     - ✅ Start Menu Shortcuts (recommended)
     - ⚠️ File Associations (optional - associates Excel files)
   - Select installation directory (default: `C:\Program Files\Army Equipment Inventory System`)
   - Click "Install"

4. **Complete Installation**
   - Wait for installation to complete
   - Click "Finish"
   - The application will be available in Start Menu and Desktop (if selected)

### Option 2: Portable Installation

1. **Download the Portable Package**
   - Download `ArmyInventorySystem_Portable.zip`
   - Extract to desired location (e.g., `C:\ArmyInventory` or USB drive)

2. **Run the Application**
   - Navigate to extracted folder
   - Double-click `ArmyInventorySystem.exe`
   - Or use the provided `run_ArmyInventorySystem.bat`

## macOS Installation

### Option 1: Using DMG Installer (Recommended)

1. **Download the DMG**
   - Download `ArmyInventorySystem_2.0.0_macOS.dmg`
   - Double-click to mount the disk image

2. **Install the Application**
   - Drag `ArmyInventorySystem.app` to the Applications folder
   - Eject the DMG when complete

3. **First Launch**
   - Open Applications folder or use Spotlight search
   - Double-click `ArmyInventorySystem`
   - If you see a security warning:
     - Go to System Preferences → Security & Privacy
     - Click "Open Anyway" for ArmyInventorySystem
     - Or right-click the app and select "Open"

### Option 2: Direct Installation

1. **Download the App Bundle**
   - Download `ArmyInventorySystem.app.zip`
   - Extract the application bundle

2. **Install Manually**
   - Copy `ArmyInventorySystem.app` to Applications folder
   - Launch from Applications or Launchpad

## First Run Setup

### Initial Database Creation

1. **Automatic Setup**
   - On first launch, the application will automatically create the database
   - A welcome message will confirm successful setup
   - Default database location:
     - Windows: `%APPDATA%\Army Equipment Inventory System\inventory.db`
     - macOS: `~/Library/Application Support/Army Equipment Inventory System/inventory.db`

2. **Import Sample Data (Optional)**
   - Use File → Import → Excel to import existing equipment data
   - The application supports Excel files (.xlsx, .xls)

### Configuration

1. **Application Settings**
   - Access via Settings menu or Preferences (macOS)
   - Configure default paths for imports/exports
   - Set up backup preferences

2. **User Preferences**
   - Customize UI theme and layout
   - Set default filters and views
   - Configure notification preferences

## Troubleshooting Installation Issues

### Windows Issues

#### "Windows protected your PC" SmartScreen Warning
**Solution:**
1. Click "More info"
2. Click "Run anyway"
3. Or add application to Windows Defender exclusions (see Antivirus Guide)

#### "The app can't run on your PC" Error
**Cause:** 32-bit Windows or very old Windows version
**Solution:**
1. Verify you have 64-bit Windows
2. Update Windows to latest version
3. Contact support if issue persists

#### Installation Fails with "Access Denied"
**Solution:**
1. Run installer as administrator
2. Temporarily disable antivirus
3. Check available disk space

#### Application Won't Start After Installation
**Solution:**
1. Check Windows Event Viewer for errors
2. Run Windows compatibility troubleshooter
3. Reinstall Microsoft Visual C++ Redistributables

### macOS Issues

#### "ArmyInventorySystem can't be opened because it's from an unidentified developer"
**Solution:**
1. Right-click the application
2. Select "Open" from context menu
3. Click "Open" in the security dialog
4. Or go to System Preferences → Security & Privacy → General → "Open Anyway"

#### Application Crashes on Launch
**Solution:**
1. Check Console app for crash logs
2. Ensure macOS is up to date
3. Try launching from Terminal to see error messages:
   ```bash
   /Applications/ArmyInventorySystem.app/Contents/MacOS/ArmyInventorySystem
   ```

#### "Damaged and can't be opened" Error
**Solution:**
1. Re-download the application
2. Clear quarantine attribute:
   ```bash
   xattr -cr /Applications/ArmyInventorySystem.app
   ```

## Network and Enterprise Deployment

### Silent Installation (Windows)

For enterprise deployment, use silent installation:

```cmd
ArmyInventorySystem_Setup.exe /S /D=C:\Program Files\Army Equipment Inventory System
```

### Group Policy Deployment

1. Create MSI package using installer
2. Deploy via Group Policy Software Installation
3. Configure antivirus exclusions via GPO

### Network Drive Installation

The application can run from network drives:
1. Install to network location
2. Create shortcuts on user desktops
3. Ensure users have read/execute permissions

## Uninstallation

### Windows
1. **Using Control Panel:**
   - Go to Control Panel → Programs and Features
   - Find "Army Equipment Inventory System"
   - Click "Uninstall"

2. **Using Settings (Windows 10/11):**
   - Go to Settings → Apps
   - Find "Army Equipment Inventory System"
   - Click "Uninstall"

### macOS
1. **Simple Removal:**
   - Drag `ArmyInventorySystem.app` from Applications to Trash
   - Empty Trash

2. **Complete Removal (including user data):**
   ```bash
   rm -rf /Applications/ArmyInventorySystem.app
   rm -rf ~/Library/Application\ Support/Army\ Equipment\ Inventory\ System
   rm -rf ~/Library/Preferences/com.army.armyinventorysystem.plist
   ```

## Data Backup and Migration

### Backup User Data
- **Database**: Copy the inventory.db file from application data directory
- **Settings**: Export settings from application preferences
- **Excel Files**: Backup any imported/exported Excel files

### Migration Between Computers
1. Install application on new computer
2. Copy database file to new location
3. Import settings if needed
4. Verify data integrity

## Support and Contact Information

### Technical Support
- **Email**: [<EMAIL>]
- **Phone**: [Support Phone Number]
- **Documentation**: [Documentation URL]

### IT Administrator Resources
- **Deployment Guide**: See DEPLOYMENT_GUIDE.md
- **Antivirus Whitelist**: See ANTIVIRUS_WHITELIST_GUIDE.md
- **Troubleshooting**: See TROUBLESHOOTING_GUIDE.md

## Version Information

- **Current Version**: 2.0.0
- **Release Date**: [Release Date]
- **Compatibility**: Windows 7+ / macOS 10.14+
- **Build Type**: Standalone executable (no Python required)
